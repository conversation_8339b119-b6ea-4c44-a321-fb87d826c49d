
// Utility functions for performance and common operations

// Configuration for Google Custom Search API
const CONFIG = {
    // Updated API configuration - Replace with your actual API credentials
    GOOGLE_API_KEY: '61201925358ea4e83', // Replace with your actual API key
    SEARCH_ENGINE_ID: 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ', // Replace with your actual Search Engine ID
    BASE_URL: 'https://www.googleapis.com/customsearch/v1',
    RESULTS_PER_PAGE: 10,
    MAX_CACHE_SIZE: 50,
    DEBOUNCE_DELAY: 300,

    // Fallback configuration for testing (these may be expired)
    FALLBACK_API_KEY: 'AIzaSyDZHL5hT78whoMeQgtj76DYpO9SoPWcFN0',
    FALLBACK_SEARCH_ENGINE_ID: '61201925358ea4e83'
};

// Cache for storing search results and suggestions
class Cache {
    constructor(maxSize = CONFIG.MAX_CACHE_SIZE) {
        this.cache = new Map();
        this.maxSize = maxSize;
    }
    
    get(key) {
        if (this.cache.has(key)) {
            // Move to end (most recently used)
            const value = this.cache.get(key);
            this.cache.delete(key);
            this.cache.set(key, value);
            return value;
        }
        return null;
    }
    
    set(key, value) {
        if (this.cache.has(key)) {
            this.cache.delete(key);
        } else if (this.cache.size >= this.maxSize) {
            // Remove least recently used
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }
    
    clear() {
        this.cache.clear();
    }
}

// Global cache instances
const searchCache = new Cache();
const suggestionCache = new Cache();

// Debounce function for performance optimization
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Throttle function for scroll events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// URL parameter utilities
function getUrlParams() {
    const params = new URLSearchParams(window.location.search);
    return {
        q: params.get('q') || '',
        start: parseInt(params.get('start')) || 1,
        type: params.get('type') || 'web'
    };
}

function updateUrlParams(params) {
    const url = new URL(window.location);
    Object.keys(params).forEach(key => {
        if (params[key]) {
            url.searchParams.set(key, params[key]);
        } else {
            url.searchParams.delete(key);
        }
    });
    window.history.pushState({}, '', url);
}

// Local storage utilities
function saveToStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (e) {
        console.warn('Failed to save to localStorage:', e);
    }
}

function getFromStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (e) {
        console.warn('Failed to read from localStorage:', e);
        return null;
    }
}

// Format numbers with commas
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// Escape HTML to prevent XSS
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Extract domain from URL
function extractDomain(url) {
    try {
        return new URL(url).hostname.replace('www.', '');
    } catch (e) {
        return url;
    }
}

// Format time ago
function timeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - new Date(date)) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    
    return new Date(date).toLocaleDateString();
}

// Performance monitoring
function measurePerformance(name, fn) {
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    console.log(`${name} took ${end - start} milliseconds`);
    return result;
}

// Error handling
function handleError(error, context = '') {
    console.error(`Error in ${context}:`, error);
    
    // Show user-friendly error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `
        <strong>Oops! Something went wrong.</strong><br>
        ${context ? `Error in ${context}. ` : ''}
        Please try again in a moment.
    `;
    
    return errorDiv;
}

// Enhanced API configuration check with fallback support
function checkApiConfiguration() {
    // Force demo mode for development/testing - set to false to use real API
    const FORCE_DEMO_MODE = false;

    if (FORCE_DEMO_MODE) {
        console.log('🔧 Running in forced demo mode - showing sample results');
        return false;
    }

    // Check primary API configuration
    const hasPrimaryConfig = CONFIG.GOOGLE_API_KEY &&
                            CONFIG.SEARCH_ENGINE_ID &&
                            CONFIG.GOOGLE_API_KEY !== 'YOUR_GOOGLE_API_KEY_HERE' &&
                            CONFIG.SEARCH_ENGINE_ID !== 'YOUR_SEARCH_ENGINE_ID_HERE' &&
                            CONFIG.GOOGLE_API_KEY.length > 10 &&
                            CONFIG.SEARCH_ENGINE_ID.length > 5;

    if (hasPrimaryConfig) {
        console.log('✅ Primary Google Custom Search API configuration found');
        return true;
    }

    // Check fallback configuration
    const hasFallbackConfig = CONFIG.FALLBACK_API_KEY &&
                             CONFIG.FALLBACK_SEARCH_ENGINE_ID &&
                             CONFIG.FALLBACK_API_KEY.length > 10 &&
                             CONFIG.FALLBACK_SEARCH_ENGINE_ID.length > 5;

    if (hasFallbackConfig) {
        console.log('⚠️ Using fallback API configuration - may have limited functionality');
        // Temporarily use fallback credentials
        CONFIG.GOOGLE_API_KEY = CONFIG.FALLBACK_API_KEY;
        CONFIG.SEARCH_ENGINE_ID = CONFIG.FALLBACK_SEARCH_ENGINE_ID;
        return true;
    }

    console.warn('❌ Google Custom Search API not configured properly. Please set your API key and Search Engine ID in js/utils.js');
    console.log('📝 Instructions:');
    console.log('1. Get API key from: https://console.developers.google.com/');
    console.log('2. Create search engine at: https://cse.google.com/');
    console.log('3. Update CONFIG.GOOGLE_API_KEY and CONFIG.SEARCH_ENGINE_ID in js/utils.js');

    return false;
}

// Test API connectivity and validity
async function testApiConnection() {
    if (!checkApiConfiguration()) {
        return { success: false, error: 'API not configured' };
    }

    try {
        console.log('🔍 Testing Google Custom Search API connection...');

        const testParams = new URLSearchParams({
            key: CONFIG.GOOGLE_API_KEY,
            cx: CONFIG.SEARCH_ENGINE_ID,
            q: 'test',
            num: 1
        });

        const testUrl = `${CONFIG.BASE_URL}?${testParams.toString()}`;

        const response = await fetch(testUrl);

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }));
            console.error('❌ API test failed:', response.status, errorData);

            return {
                success: false,
                error: `API Error ${response.status}: ${errorData.error?.message || 'Unknown error'}`,
                status: response.status
            };
        }

        const data = await response.json();
        console.log('✅ API test successful:', data.searchInformation);

        return {
            success: true,
            totalResults: data.searchInformation?.totalResults || '0',
            searchTime: data.searchInformation?.searchTime || '0'
        };

    } catch (error) {
        console.error('❌ API test failed with exception:', error);
        return {
            success: false,
            error: `Network error: ${error.message}`
        };
    }
}

// Enhanced API configuration check with live testing
async function verifyApiConfiguration() {
    const configCheck = checkApiConfiguration();
    if (!configCheck) {
        return { configured: false, working: false, message: 'API not configured' };
    }

    const testResult = await testApiConnection();

    return {
        configured: true,
        working: testResult.success,
        message: testResult.success ?
            `API working - ${testResult.totalResults} results available` :
            testResult.error,
        details: testResult
    };
}

// Lazy loading for images
function setupLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

// Initialize performance optimizations
document.addEventListener('DOMContentLoaded', () => {
    // Setup lazy loading
    setupLazyLoading();
    
    // Preload critical resources
    const criticalResources = [
        'styles/main.css',
        'js/search.js',
        'js/autocomplete.js'
    ];
    
    criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource;
        link.as = resource.endsWith('.css') ? 'style' : 'script';
        document.head.appendChild(link);
    });
});

// Export for use in other modules
window.Utils = {
    CONFIG,
    Cache,
    searchCache,
    suggestionCache,
    debounce,
    throttle,
    getUrlParams,
    updateUrlParams,
    saveToStorage,
    getFromStorage,
    formatNumber,
    escapeHtml,
    extractDomain,
    timeAgo,
    measurePerformance,
    handleError,
    checkApiConfiguration,
    setupLazyLoading
};

