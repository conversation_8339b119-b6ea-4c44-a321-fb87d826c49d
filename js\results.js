// Results page functionality for displaying Google Custom Search results

class ResultsPage {
    constructor() {
        this.currentQuery = '';
        this.currentPage = 1;
        this.currentType = 'web';
        this.totalResults = 0;
        this.isLoading = false;
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadFromUrl();
        
        // Check API configuration and proceed with search
        if (!Utils.checkApiConfiguration()) {
            console.log('API not configured, will use demo data as fallback');
        } else {
            console.log('Google Custom Search API configured and ready');
        }
        
        // Check for instant answer first
        this.checkForInstantAnswer();

        // Perform initial search if query exists
        if (this.currentQuery) {
            this.performSearch();
        }
    }
    
    bindEvents() {
        const searchInput = document.getElementById('search-input');
        const searchIcon = document.getElementById('search-btn');
        
        // Search input events
        if (searchInput) {
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const query = searchInput.value.trim();
                    if (query) {
                        this.updateSearch(query, 1, this.currentType);
                    }
                }
            });
        }
        
        if (searchIcon) {
            searchIcon.addEventListener('click', () => {
                const query = searchInput?.value.trim() || '';
                if (query) {
                    this.updateSearch(query, 1, this.currentType);
                }
            });
        }
        
        // Navigation tabs
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const type = tab.getAttribute('data-type');
                this.updateSearch(this.currentQuery, 1, type);
            });
        });
        
        // Handle browser back/forward
        window.addEventListener('popstate', () => {
            this.loadFromUrl();
            this.performSearch();
        });
    }
    
    loadFromUrl() {
        const params = Utils.getUrlParams();
        this.currentQuery = params.q;
        this.currentPage = Math.max(1, Math.floor((params.start - 1) / Utils.CONFIG.RESULTS_PER_PAGE) + 1);
        this.currentType = params.type;
        
        // Update UI
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.value = this.currentQuery;
        }
        
        // Update page title
        if (this.currentQuery) {
            document.title = `${this.currentQuery} - Search Results`;
        }
        
        // Update active tab
        this.updateActiveTab();
    }
    
    updateActiveTab() {
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
            if (tab.getAttribute('data-type') === this.currentType) {
                tab.classList.add('active');
            }
        });
    }
    
    updateSearch(query, page = 1, type = 'web') {
        this.currentQuery = query;
        this.currentPage = page;
        this.currentType = type;
        
        const start = (page - 1) * Utils.CONFIG.RESULTS_PER_PAGE + 1;
        
        // Update URL
        Utils.updateUrlParams({
            q: query,
            start: start,
            type: type
        });
        
        // Update page title
        document.title = `${query} - Search Results`;
        
        // Update active tab
        this.updateActiveTab();
        
        // Perform search
        this.performSearch();
    }
    
    async performSearch() {
        if (!this.currentQuery || this.isLoading) return;
        
        this.isLoading = true;
        this.showLoading();
        
        try {
            const start = (this.currentPage - 1) * Utils.CONFIG.RESULTS_PER_PAGE + 1;
            const results = await this.searchGoogle(this.currentQuery, start, this.currentType);
            
            this.hideLoading();
            this.renderResults(results);
            this.renderPagination(results);
            
        } catch (error) {
            console.error('Search failed:', error);
            this.hideLoading();
            this.showError(error);
        } finally {
            this.isLoading = false;
        }
    }
    
    async searchGoogle(query, start = 1, searchType = 'web') {
        const cacheKey = `${query}-${start}-${searchType}`;

        // Check cache first
        const cached = Utils.searchCache.get(cacheKey);
        if (cached) {
            return cached;
        }

        // Check if API is configured, if not return demo data
        if (!Utils.checkApiConfiguration()) {
            return this.getDemoResults(query, start, searchType);
        }

        try {
            // Build API URL
            const params = new URLSearchParams({
                key: Utils.CONFIG.GOOGLE_API_KEY,
                cx: Utils.CONFIG.SEARCH_ENGINE_ID,
                q: query,
                start: start,
                num: Utils.CONFIG.RESULTS_PER_PAGE
            });

            // Add advanced search type specific parameters for optimal results
            if (searchType === 'images') {
                params.append('searchType', 'image');
                params.append('imgSize', 'xlarge');
                params.append('imgType', 'photo');
                params.append('imgColorType', 'color');
                params.append('safe', 'active');
                params.append('rights', 'cc_publicdomain,cc_attribute,cc_sharealike,cc_noncommercial,cc_nonderived');
                params.append('fileType', 'jpg,png,gif,webp,svg');
                params.append('filter', '1');
                params.append('exactTerms', query.split(' ').slice(0, 3).join(' '));
                params.append('siteSearch', 'unsplash.com OR pixabay.com OR pexels.com OR wikimedia.org OR flickr.com');
            } else if (searchType === 'videos') {
                params.set('q', `${query} video tutorial watch lesson course documentary site:youtube.com OR site:vimeo.com OR site:dailymotion.com OR site:ted.com OR site:coursera.org`);
                params.append('dateRestrict', 'y2');
                params.append('filter', '1');
                params.append('excludeTerms', 'spam fake clickbait');
                params.append('lr', 'lang_en');
            } else if (searchType === 'news') {
                params.append('siteSearch', 'reuters.com OR bbc.com OR cnn.com OR apnews.com OR npr.org OR theguardian.com OR wsj.com OR nytimes.com OR washingtonpost.com OR bloomberg.com');
                params.append('sort', 'date');
                params.append('dateRestrict', 'd3');
                params.append('orTerms', 'news breaking latest update report today current events');
                params.append('filter', '1');
                params.append('excludeTerms', 'opinion editorial blog personal');
                params.append('lr', 'lang_en');
            } else if (searchType === 'shopping') {
                params.append('siteSearch', 'amazon.com OR ebay.com OR walmart.com OR target.com OR bestbuy.com OR etsy.com OR shopify.com OR costco.com OR homedepot.com');
                params.append('orTerms', 'buy price product store shop deal discount sale review rating customer');
                params.append('cr', 'countryUS');
                params.append('filter', '1');
                params.append('excludeTerms', 'fake counterfeit scam');
                params.append('dateRestrict', 'm6');
                params.append('lr', 'lang_en');
            } else if (searchType === 'books') {
                params.append('siteSearch', 'amazon.com OR goodreads.com OR books.google.com OR worldcat.org OR barnesandnoble.com OR openlibrary.org OR jstor.org OR scholar.google.com');
                params.append('orTerms', 'book author read library isbn publisher review summary academic textbook');
                params.append('fileType', 'pdf,epub,mobi,djvu');
                params.append('filter', '1');
                params.append('rights', 'cc_publicdomain,cc_attribute,cc_sharealike');
                params.append('excludeTerms', 'pirated illegal download torrent');
                params.append('lr', 'lang_en');
            }

            const url = `${Utils.CONFIG.BASE_URL}?${params.toString()}`;
            console.log('Making API request to:', url);

            const response = await fetch(url);

            if (!response.ok) {
                console.error('API request failed:', response.status, response.statusText);
                throw new Error(`Search failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            console.log('API response:', data);

            // Cache the results
            Utils.searchCache.set(cacheKey, data);

            return data;
        } catch (error) {
            console.error('Search API error, falling back to demo data:', error);
            return this.getDemoResults(query, start, searchType);
        }
    }

    getDemoResults(query, start = 1, searchType = 'web') {
        // Return demo data for testing
        const demoData = {
            searchInformation: {
                totalResults: "1234567",
                searchTime: 0.45
            },
            items: [
                {
                    title: `Demo Result 1 for "${query}"`,
                    link: "https://example.com/1",
                    snippet: "This is a demo search result. The actual Google Custom Search API is not configured yet. Please follow the setup instructions in the README to get real search results.",
                    displayLink: "example.com"
                },
                {
                    title: `Demo Result 2 for "${query}"`,
                    link: "https://example.com/2",
                    snippet: "Another demo result showing how the search interface works. Configure your Google API key and Search Engine ID to see real results.",
                    displayLink: "example.com"
                },
                {
                    title: `Demo Result 3 for "${query}"`,
                    link: "https://example.com/3",
                    snippet: "This demonstrates the search results layout and functionality. The interface is fully working and ready for real API integration.",
                    displayLink: "example.com"
                },
                {
                    title: `Demo Result 4 for "${query}"`,
                    link: "https://example.com/4",
                    snippet: "Fast, responsive search results with proper pagination and multiple search types. Built with pure JavaScript for maximum performance.",
                    displayLink: "example.com"
                },
                {
                    title: `Demo Result 5 for "${query}"`,
                    link: "https://example.com/5",
                    snippet: "High-performance Google clone with autocomplete, caching, and offline support. No heavy frameworks - just optimized vanilla JavaScript.",
                    displayLink: "example.com"
                }
            ]
        };

        // Add more results for pagination testing
        if (start > 1) {
            demoData.items = demoData.items.map((item, index) => ({
                ...item,
                title: `${item.title} - Page ${Math.ceil(start / 10)}`,
                snippet: `${item.snippet} (Result ${start + index})`
            }));
        }

        return demoData;
    }
    
    renderResults(data) {
        const container = document.getElementById('results-container');
        const infoElement = document.getElementById('results-info');
        
        if (!container) return;
        
        // Update results info
        if (data.searchInformation && infoElement) {
            const totalResults = parseInt(data.searchInformation.totalResults);
            const searchTime = parseFloat(data.searchInformation.searchTime);
            infoElement.textContent = `About ${Utils.formatNumber(totalResults)} results (${searchTime} seconds)`;
            this.totalResults = totalResults;
        }
        
        // Clear previous results
        container.innerHTML = '';
        
        if (!data.items || data.items.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #70757a;">
                    <p>No results found for "${Utils.escapeHtml(this.currentQuery)}"</p>
                    <p style="margin-top: 10px; font-size: 14px;">Try different keywords or check your spelling.</p>
                </div>
            `;
            return;
        }
        
        // Render results based on type
        if (this.currentType === 'images') {
            this.renderImageResults(data.items, container);
        } else {
            this.renderWebResults(data.items, container);
        }
    }
    
    renderWebResults(items, container) {
        items.forEach((item, index) => {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result-item';
            resultDiv.style.animationDelay = `${index * 0.1}s`;

            const domain = Utils.extractDomain(item.link);
            const title = Utils.escapeHtml(item.title);
            const snippet = Utils.escapeHtml(item.snippet || '');

            // Highlight search terms in snippet
            const highlightedSnippet = this.highlightSearchTerms(snippet, this.currentQuery);

            resultDiv.innerHTML = `
                <div class="result-url">
                    <div class="favicon"></div>
                    <span class="breadcrumb">${domain}</span>
                </div>
                <a href="${item.link}" class="result-title" target="_blank" rel="noopener">
                    ${title}
                </a>
                <div class="result-snippet">${highlightedSnippet}</div>
            `;

            container.appendChild(resultDiv);
        });
    }

    highlightSearchTerms(text, query) {
        if (!query || query.length < 2) return text;

        const terms = query.split(' ').filter(term => term.length > 1);
        let highlightedText = text;

        terms.forEach(term => {
            const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<em>$1</em>');
        });

        return highlightedText;
    }
    
    renderImageResults(items, container) {
        const gridDiv = document.createElement('div');
        gridDiv.style.cssText = `
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        `;
        
        items.forEach(item => {
            const imageDiv = document.createElement('div');
            imageDiv.style.cssText = `
                border-radius: 8px;
                overflow: hidden;
                background: #f8f9fa;
                cursor: pointer;
            `;
            
            imageDiv.innerHTML = `
                <img 
                    src="${item.link}" 
                    alt="${Utils.escapeHtml(item.title)}"
                    style="width: 100%; height: 150px; object-fit: cover;"
                    loading="lazy"
                    onerror="this.style.display='none'"
                >
                <div style="padding: 10px; font-size: 12px; color: #5f6368;">
                    ${Utils.escapeHtml(item.title)}
                </div>
            `;
            
            imageDiv.addEventListener('click', () => {
                window.open(item.image?.contextLink || item.link, '_blank');
            });
            
            gridDiv.appendChild(imageDiv);
        });
        
        container.appendChild(gridDiv);
    }
    
    renderPagination(data) {
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) return;

        paginationContainer.innerHTML = '';

        const totalPages = Math.min(10, Math.ceil(this.totalResults / Utils.CONFIG.RESULTS_PER_PAGE));

        if (totalPages <= 1) return;

        // Create pagination navigation
        const paginationNav = document.createElement('div');
        paginationNav.className = 'pagination-nav';

        // Previous button
        if (this.currentPage > 1) {
            const prevBtn = this.createPageButton('< Previous', this.currentPage - 1);
            paginationNav.appendChild(prevBtn);
        }

        // Google logo in pagination
        const logoDiv = document.createElement('div');
        logoDiv.className = 'google-logo-pagination';
        logoDiv.innerHTML = `
            <svg viewBox="0 0 272 92" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0)">
                    <path d="M115.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18C71.25 34.32 81.24 25 93.5 25s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44S80.99 39.2 80.99 47.18c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#EA4335"/>
                    <path d="M163.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18c0-12.85 9.99-22.18 22.25-22.18s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44s-12.51 5.46-12.51 13.44c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#FBBC05"/>
                    <path d="M209.75 26.34v39.82c0 16.38-9.66 23.07-21.08 23.07-10.75 0-17.22-7.19-19.66-13.07l8.48-3.53c1.51 3.61 5.21 7.87 11.17 7.87 7.31 0 11.84-4.51 11.84-13v-3.19h-.34c-2.18 2.69-6.38 5.04-11.68 5.04-11.09 0-21.25-9.66-21.25-22.09 0-12.52 10.16-22.26 21.25-22.26 5.29 0 9.49 2.35 11.68 4.96h.34v-3.61h9.25zm-8.56 20.92c0-7.81-5.21-13.52-11.84-13.52-6.72 0-12.35 5.71-12.35 13.52 0 7.73 5.63 13.36 12.35 13.36 6.63 0 11.84-5.63 11.84-13.36z" fill="#4285F4"/>
                    <path d="M225 3v65h-9.5V3h9.5z" fill="#34A853"/>
                    <path d="M262.02 54.48l7.56 5.04c-2.44 3.61-8.32 9.83-18.48 9.83-12.6 0-22.01-9.74-22.01-22.18 0-13.19 9.49-22.18 20.92-22.18 11.51 0 17.14 9.16 18.98 14.11l1.01 2.52-29.65 12.28c2.27 4.45 5.8 6.72 10.75 6.72 4.96 0 8.4-2.44 10.92-6.14zm-23.27-7.98l19.82-8.23c-1.09-2.77-4.37-4.7-8.23-4.7-4.95 0-11.84 4.37-11.59 12.93z" fill="#EA4335"/>
                    <path d="M35.29 41.41V32H67c.31 1.64.47 3.58.47 5.68 0 7.06-1.93 15.79-8.15 22.01-6.05 6.3-13.78 9.66-24.02 9.66C16.32 69.35.36 53.89.36 34.91.36 15.93 16.32.47 35.3.47c10.5 0 17.98 4.12 23.6 9.49l-6.64 6.64c-4.03-3.78-9.49-6.72-16.97-6.72-13.86 0-24.7 11.17-24.7 25.03 0 13.86 10.84 25.03 24.7 25.03 8.99 0 14.11-3.61 17.39-6.89 2.66-2.66 4.41-6.46 5.1-11.65l-22.49.01z" fill="#4285F4"/>
                </g>
                <defs>
                    <clipPath id="clip0">
                        <rect width="272" height="92" fill="white"/>
                    </clipPath>
                </defs>
            </svg>
        `;
        paginationNav.appendChild(logoDiv);

        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, startPage + 4);

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = this.createPageNumber(i.toString(), i, i === this.currentPage);
            paginationNav.appendChild(pageBtn);
        }

        // Next button
        if (this.currentPage < totalPages) {
            const nextBtn = this.createPageButton('Next >', this.currentPage + 1);
            paginationNav.appendChild(nextBtn);
        }

        paginationContainer.appendChild(paginationNav);
    }
    
    createPageButton(text, page, isActive = false) {
        const button = document.createElement('button');
        button.className = `page-btn ${isActive ? 'active' : ''}`;
        button.textContent = text;
        button.disabled = isActive;

        if (!isActive) {
            button.addEventListener('click', () => {
                this.updateSearch(this.currentQuery, page, this.currentType);
            });
        }

        return button;
    }

    createPageNumber(text, page, isActive = false) {
        const link = document.createElement('a');
        link.className = `page-number ${isActive ? 'current' : ''}`;
        link.textContent = text;
        link.href = '#';

        if (!isActive) {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.updateSearch(this.currentQuery, page, this.currentType);
            });
        } else {
            link.addEventListener('click', (e) => {
                e.preventDefault();
            });
        }

        return link;
    }
    
    showLoading() {
        const loading = document.getElementById('loading');
        const container = document.getElementById('results-container');
        const pagination = document.getElementById('pagination');
        
        if (loading) loading.style.display = 'block';
        if (container) container.innerHTML = '';
        if (pagination) pagination.innerHTML = '';
    }
    
    hideLoading() {
        const loading = document.getElementById('loading');
        if (loading) loading.style.display = 'none';
    }
    
    showError(error) {
        const container = document.getElementById('results-container');
        if (!container) return;
        
        const errorDiv = Utils.handleError(error, 'search');
        container.appendChild(errorDiv);
    }
    
    checkForInstantAnswer() {
        const urlParams = Utils.getUrlParams();
        if (urlParams.instant === 'true') {
            const instantAnswer = sessionStorage.getItem('instantAnswer');
            if (instantAnswer) {
                try {
                    const answer = JSON.parse(instantAnswer);
                    this.displayInstantAnswer(answer);
                    sessionStorage.removeItem('instantAnswer');
                } catch (error) {
                    console.error('Failed to parse instant answer:', error);
                }
            }
        }
    }

    displayInstantAnswer(answer) {
        const container = document.getElementById('results-container');
        if (!container) return;

        const instantAnswerDiv = document.createElement('div');
        instantAnswerDiv.className = 'instant-answer-container';
        instantAnswerDiv.innerHTML = answer.html;

        // Insert at the beginning of results
        container.insertBefore(instantAnswerDiv, container.firstChild);
    }

    showApiConfigurationWarning() {
        const container = document.getElementById('results-container');
        if (!container) return;

        container.innerHTML = `
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; color: #856404; margin: 20px 0;">
                <strong>⚠️ API Configuration Required</strong><br><br>
                To display real search results, please configure your Google Custom Search API:
                <ol style="margin: 15px 0; padding-left: 20px;">
                    <li>Get a Google API key from <a href="https://console.developers.google.com/" target="_blank">Google Cloud Console</a></li>
                    <li>Create a Custom Search Engine at <a href="https://cse.google.com/" target="_blank">Google CSE</a></li>
                    <li>Update the GOOGLE_API_KEY and SEARCH_ENGINE_ID in js/utils.js</li>
                </ol>
                <small>The search interface is fully functional and ready to use once configured.</small>
            </div>
        `;
    }
}

// Initialize results page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.resultsPage = new ResultsPage();
});

// Export for use in other modules
window.ResultsPage = ResultsPage;
